CurrentXP = 0
CurrentRank = 0
Leaderboard = nil
Players = {}
Player = nil
UIActive = true
Ready = false
local dubleXP = false

Citizen.CreateThread(function()
    -- Wait for ESX
        
    -- Wait for ESX player
    while not ESX.IsPlayerLoaded() do
        Citizen.Wait(10)
    end

    if not Ready then
        TriggerServerEvent("zahya_xplevel:InitialPlayerServerSide")
    end    

    Citizen.Wait(1000* 25)
    TriggerServerEvent("zahya_xplevel:checkStoreDoubleXP")
end)	

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    while ESX == nil do Citizen.Wait(0) end
	PlayerData = xPlayer	
end)

------------------------------------------------------------
--                        CONTROLS                        --
------------------------------------------------------------

------------------------------------------------------------
--                        COMMANDS                        --
------------------------------------------------------------
TriggerEvent('chat:addSuggestion', '/ESXP', 'Display your XP stats')

RegisterCommand('ESXP', function(source, args)
    Citizen.CreateThread(function()
        local xpToNext = ESXP_GetXPToNextRank()

        -- SHOW THE XP BAR
        SendNUIMessage({ xpm_display = true })        

        TriggerEvent('chat:addMessage', {
            color = { 255, 0, 0},
            multiline = true,
            args = {"SYSTEM", _('cmd_current_xp', CurrentXP)}
        })
        TriggerEvent('chat:addMessage', {
            color = { 255, 0, 0},
            multiline = true,
            args = {"SYSTEM", _('cmd_current_lvl', CurrentRank)}
        })
        TriggerEvent('chat:addMessage', {
            color = { 255, 0, 0},
            multiline = true,
            args = {"SYSTEM", _('cmd_next_lvl', xpToNext, CurrentRank + 1)}
        })                
    end)
end)


RegisterNetEvent('zahya_xplevel:getRank_cl')
AddEventHandler('zahya_xplevel:getRank_cl', function()
	TriggerServerEvent("zahya_xplevel:SendRank", exports.zahya_xplevel:ESXP_GetRank())
end)

RegisterNetEvent("zahya_xplevel:Promotion_client")
AddEventHandler("zahya_xplevel:Promotion_client", function(duble)
    local src = source
    dubleXP = duble
end)
