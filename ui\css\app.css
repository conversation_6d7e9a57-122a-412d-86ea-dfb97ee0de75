@font-face {
    font-family: "ChaletComprime";
    src: url("../fonts/ChaletComprimeCologneSixty.ttf");
}

body {
    margin: 0;
}

#xpm_main {
    opacity: 0;
    -webkit-transition: opacity 1000ms;
    transition: opacity 1000ms;
}

#xpm_main.active, #xpm_main.active.show-leaderboard  #xpm_leaderboard {
    opacity: 1;
    -webkit-transition: opacity 250ms;
    transition: opacity 250ms;
}

.xpm {
    font-family: "ChaletComprime";
    width: 100vw;
    position: absolute;
    top: 16px;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
}

.xpm .xpm-inner {
    width: 532px;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.xpm .xpm-inner .xpm-progress {
    width: calc(100% - 130px);
    height: 7px;
    position: relative;
    display: -webkit-box;
    display: flex;
    justify-content: space-around;
    margin-top: 1px;
}

.xpm .xpm-inner .xpm-progress .xpm-segment {
    height: 100%;
    background-color: #1c2b43;
    margin-right: 4px;
    width: 100%;
    position: relative;
}

.xpm .xpm-inner .xpm-progress .xpm-segment:last-child {
    margin-right: 0;
}

.xpm .xpm-inner .xpm-progress .xpm-segment .xpm-indicator--bar {
    position: absolute;
    height: 100%;
    width: 0%;
    background-color: white;
}

.xpm .xpm-inner .xpm-progress .xpm-segment .xpm-progress--bar {
    position: absolute;
    z-index: 1;
    height: 100%;
    width: 0%;
    background-color: #2e6eb8;
}

.xpm .xpm-inner .xpm-progress.xpm-remove .xpm-progress--bar {
    background-color: #D50000;
}

.xpm .xpm-inner .xpm-progress.xpm-remove .xpm-indicator--bar {
    background-color: #FF8A80;
}

.xpm .xpm-inner .xpm-rank {
    width: 43px;
    height: 43px;
    border-radius: 50%;
    background-color: #000;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    color: #fff;
    position: relative;
}

.xpm .xpm-inner .xpm-rank::before {
    position: absolute;
    width: 49px;
    height: 49px;
    border-radius: 50%;
    background-color: #000;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    color: #fff;
    content: "";
}

.xpm .xpm-inner .xpm-rank div {
    position: absolute;
    font-size: 40px;
    z-index: 10;
}

.xpm .xpm-inner .xpm-rank svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    stroke: #2e6eb8;
    fill: none;
    stroke-width: 80;
}

.xpm .xpm-inner .xpm-rank.spin div {
    -webkit-animation: spin 250ms linear 0ms forwards;
    animation: spin 250ms linear 0ms forwards;
}

.xpm .xpm-inner .xpm-rank.pulse {
    background-color: transparent;
    -webkit-animation: pulse 250ms linear 0ms forwards;
    animation: pulse 250ms linear 0ms forwards;
}

.xpm .xpm-inner .xpm-rank.pulse::before {
    background-color: transparent;
}

.xpm .xpm-inner .xpm-rank.pulse div {
    color: #2e6eb8;
}

.xpm .xpm-inner .xpm-rank.pulse svg {
    fill: none;
    stroke: #fff;
}

.xpm .xpm-inner .xpm-rank.highlight {
    background-color: transparent;
}

.xpm .xpm-inner .xpm-rank.highlight::before {
    background-color: transparent;
}

.xpm .xpm-inner .xpm-rank.highlight div {
    color: #2e6eb8;
}

.xpm .xpm-inner .xpm-rank.highlight svg {
    fill: none;
    stroke: #fff;
}

.xpm .xpm-data {
    width: 532px;
    font-size: 28px;
    color: #fff;
    display: -webkit-box;
    display: flex;
    justify-content: space-around;
    position: absolute;
    top: 31px;
}

.xpm .xpm-data span {
    width: 48%;
    display: inline-block;
    position: relative;
    text-shadow: -1px -1px 2px #000, 1px -1px 2px #000, -1px 1px 2px #000, 1px 1px 2px #000;
}

.xpm .xpm-data span:first-child {
    text-align: right;
    padding-right: 2px;
}

.xpm .xpm-data span:first-child::after {
    position: absolute;
    right: -8px;
    content: "/";
}

.xpm .xpm-data span:last-child {
    padding-left: 2px;
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg);
    }

    to {
        -webkit-transform: rotateY(360deg);
        transform: rotateY(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg);
    }

    to {
        -webkit-transform: rotateY(360deg);
        transform: rotateY(360deg);
    }
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scale(1.25);
        transform: scale(1.25);
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        -webkit-transform: scale(1.25);
        transform: scale(1.25);
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

/* LEADERBOARD */
#xpm_leaderboard {
    position: absolute;
    left: 50px;
    top: 25px;
    width: 20%;
    font-family: "ChaletComprime";
    color: #fff;
    font-size: 25px;
    opacity: 0;
    -webkit-transition: opacity 1000ms;
    transition: opacity 1000ms;
}

.xpm-leaderboard--header {
    background-color: rgba(0, 0, 0, 0.4);
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;	    
}

.xpm-leaderboard--players {
    padding: 0;
    margin: 0;
    background-color: rgba(35, 132, 255, 0.4);
}

.xpm-leaderboard--players .xpm-leaderboard--player {
    list-style: none;
    padding: 5px 10px;
    height: 30px;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    position: relative;
}

.xpm-leaderboard--players .xpm-leaderboard--player::before {
    position: absolute;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: rgba(35, 132, 255, 0.4);
    content: "";
}

.xpm-leaderboard--players .xpm-leaderboard--player.hidden {
    display: none;
}

.xpm-leaderboard--players .xpm-leaderboard--playerinfo {
    width: 100%;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.xpm-leaderboard--players .xpm-leaderboard--playerping {
    font-size: 20px;
    margin: 0 10px;
}

.xpm-leaderboard--players .xpm-leaderboard--playerrank {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: end;
    justify-content: end;
    width: 30px;
    height: 30px;
    position: relative;
}

.xpm-leaderboard--players .xpm-leaderboard--playerrank::before {
    position: absolute;
    top: -2px;
    left: -2px;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background-color: #000;
    z-index: 1;
    content: "";
}

.xpm-leaderboard--players .xpm-leaderboard--playerrank svg {
    position: relative;
    width: 30px;
    height: 30px;
    stroke: #2e6eb8;
    fill: none;
    stroke-width: 80;
    z-index: 2;
}

.xpm-leaderboard--players .xpm-leaderboard--playerrank .xpm-leaderboard--playerranknum {
    position: absolute;
    width: 30px;
    height: 30px;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-family: "ChaletComprime";
    font-size: 30px;
    z-index: 2;
}